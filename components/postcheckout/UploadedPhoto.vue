<template>
  <div
    class="group relative rounded-lg border border-transparent"
    :class="{
      'opacity-50': isDragging,
      'cursor-move hover:border-sky-500 hover:border-dashed': draggable && !isMobile
    }"
    :draggable="draggable && !isMobile"
    v-on="draggable && !isMobile ? { dragstart: onDragStart, dragend: onDragEnd } : {}"
  >
    <img :key="photoSrc" class="object-cover w-full h-full aspect-square rounded-lg" src="/avatar-danny.jpeg" alt="" @error="loadingError">
    <div v-if="isLoading" class="absolute inset-0 flex items-center justify-center bg-gray-900/30 rounded-lg">
      <svg
        class="w-5 h-5 text-white animate-spin"
        xmlns="http://www.w3.org/2000/svg"
        fill="none"
        viewBox="0 0 24 24"
      >
        <circle
          class="opacity-25"
          cx="12"
          cy="12"
          r="10"
          stroke="currentColor"
          stroke-width="4"
        />
        <path
          class="opacity-75"
          fill="currentColor"
          d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"
        />
      </svg>
    </div>
    <div
      v-else-if="isSuccess"
      class="absolute inset-0 flex items-center justify-center bg-green-500/30 transition rounded-lg"
      :class="{
        'opacity-0': photo.status === 'success'
      }"
    >
      <svg
        aria-hidden="true"
        class="size-6"
        viewBox="0 0 27 26"
        fill="none"
        xmlns="http://www.w3.org/2000/svg"
      >
        <rect x="0.5" width="26" height="26" rx="13" fill="#4ADE80" />
        <g clip-path="url(#clip0_2011_15934)">
          <path
            fill-rule="evenodd"
            clip-rule="evenodd"
            d="M19.6281 8.64302C19.8884 8.90337 19.8884 9.32548 19.6281 9.58583L12.1327 17.0812C11.8463 17.3675 11.382 17.3675 11.0956 17.0812L7.37154 13.3571C7.1112 13.0967 7.1112 12.6746 7.37154 12.4143C7.63189 12.1539 8.054 12.1539 8.31435 12.4143L11.6142 15.7141L18.6853 8.64302C18.9456 8.38267 19.3677 8.38267 19.6281 8.64302Z"
            fill="#166534"
          />
        </g>
      </svg>
    </div>

    <!-- Desktop delete button (hover to show) -->
    <button v-if="isSuccess && !isMobile" type="button" class="absolute -top-1 -right-1 -m-1 hidden group-hover:block" aria-label="Delete photo" @click="deletePhoto">
      <svg
        aria-hidden="true"
        class="size-6"
        viewBox="0 0 27 26"
        fill="none"
        xmlns="http://www.w3.org/2000/svg"
      >
        <rect x="0.5" width="26" height="26" rx="13" fill="#F87171" />
        <g clip-path="url(#clip0_2011_15945)">
          <path
            fill-rule="evenodd"
            clip-rule="evenodd"
            d="M13.5 13.9429L17.2712 17.7142C17.5315 17.9745 17.9537 17.9745 18.214 17.7142C18.4743 17.4538 18.4743 17.0317 18.214 16.7713L14.4428 13.0001L18.214 9.2289C18.4743 8.96855 18.4743 8.54644 18.214 8.28609C17.9537 8.02574 17.5315 8.02574 17.2712 8.28609L13.5 12.0573L9.72873 8.28608C9.46839 8.02573 9.04627 8.02573 8.78593 8.28608C8.52558 8.54643 8.52558 8.96854 8.78593 9.22889L12.5571 13.0001L8.78593 16.7714C8.52558 17.0317 8.52558 17.4538 8.78593 17.7142C9.04627 17.9745 9.46839 17.9745 9.72873 17.7142L13.5 13.9429Z"
            fill="#991B1B"
          />
        </g>
      </svg>
    </button>

    <!-- Mobile dropdown menu -->
    <div v-if="isSuccess && isMobile" class="absolute -top-1 -right-1 -m-1">
      <ButtonDropdown
        theme="v2"
        title="Photo options"
        :items="mobileMenuItems"
        size="sm"
        xalign="right"
        valign="bottom"
        button-class="!size-6 !p-0 !bg-gray-500 hover:!bg-gray-600"
        dropdown-class="!w-48"
        @select="handleMobileAction"
      />
    </div>

    <!--    <div v-if="isSuccess" class="absolute opacity-0 inset-0 flex items-center justify-center bg-red-500/30 cursor-pointer hover:opacity-100" @click="deletePhoto">-->
    <!--      <svg-->
    <!--        aria-hidden="true"-->
    <!--        class="size-6"-->
    <!--        viewBox="0 0 27 26"-->
    <!--        fill="none"-->
    <!--        xmlns="http://www.w3.org/2000/svg"-->
    <!--      >-->
    <!--        <rect x="0.5" width="26" height="26" rx="13" fill="#F87171" />-->
    <!--        <g clip-path="url(#clip0_2011_15945)">-->
    <!--          <path-->
    <!--            fill-rule="evenodd"-->
    <!--            clip-rule="evenodd"-->
    <!--            d="M13.5 13.9429L17.2712 17.7142C17.5315 17.9745 17.9537 17.9745 18.214 17.7142C18.4743 17.4538 18.4743 17.0317 18.214 16.7713L14.4428 13.0001L18.214 9.2289C18.4743 8.96855 18.4743 8.54644 18.214 8.28609C17.9537 8.02574 17.5315 8.02574 17.2712 8.28609L13.5 12.0573L9.72873 8.28608C9.46839 8.02573 9.04627 8.02573 8.78593 8.28608C8.52558 8.54643 8.52558 8.96854 8.78593 9.22889L12.5571 13.0001L8.78593 16.7714C8.52558 17.0317 8.52558 17.4538 8.78593 17.7142C9.04627 17.9745 9.46839 17.9745 9.72873 17.7142L13.5 13.9429Z"-->
    <!--            fill="#991B1B"-->
    <!--          />-->
    <!--        </g>-->
    <!--      </svg>-->
    <!--    </div>-->
  </div>
</template>

<script>
export default {
  props: {
    photo: {
      type: Object,
      required: true
    },
    draggable: {
      type: Boolean,
      default: false
    }
  },
  data () {
    return {
      random: null,
      isDragging: false
    }
  },
  computed: {
    isLoading () {
      return this.photo.status === 'pending'
    },
    isError () {
      return this.photo.status === 'error'
    },
    isSuccess () {
      return this.photo.status === 'pre-success' || this.photo.status === 'success'
    },
    photoSrc () {
      if (this.photo.file) {
        return URL.createObjectURL(this.photo.file)
      }

      if (this.random) {
        const param = this.photo.url.includes('?') ? '&' : '?'
        return this.photo.url + param + 't=' + this.random
      }

      return this.photo.url
    },
    isMobile () {
      if (typeof window === 'undefined') {
        return false
      }
      return /Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(navigator.userAgent)
    },
    mobileMenuItems () {
      const items = [
        {
          title: this.$t('Delete'),
          value: 'delete',
          icon: 'IconTrash',
          classes: 'text-red-500 hover:bg-red-50'
        }
      ]

      if (this.photo.fullBody) {
        items.push({
          title: this.$t('Move to face photos'),
          value: 'move-to-face',
          icon: 'IconUser',
          classes: 'text-blue-500 hover:bg-blue-50'
        })
      } else {
        items.push({
          title: this.$t('Move to full body photos'),
          value: 'move-to-fullbody',
          icon: 'IconUserGroup',
          classes: 'text-green-500 hover:bg-green-50'
        })
      }

      return items
    }
  },
  mounted () {
    // Close mobile menu when clicking outside
    document.addEventListener('click', this.handleClickOutside)
  },
  beforeDestroy () {
    document.removeEventListener('click', this.handleClickOutside)
  },
  methods: {
    deletePhoto () {
      if (!confirm(this.$t('Are you sure you want to delete this photo?'))) {
        return
      }

      this.$store.dispatch('onboarding/deletePhoto', { md5: this.photo.md5 })
    },
    moveToFacePhotos () {
      this.$store.dispatch('onboarding/updatePhotoCategory', {
        md5: this.photo.md5,
        fullBody: false
      })
    },
    moveToFullBodyPhotos () {
      this.$store.dispatch('onboarding/updatePhotoCategory', {
        md5: this.photo.md5,
        fullBody: true
      })
    },
    handleMobileAction (action) {
      switch (action) {
        case 'delete':
          this.deletePhoto()
          break
        case 'move-to-face':
          this.moveToFacePhotos()
          break
        case 'move-to-fullbody':
          this.moveToFullBodyPhotos()
          break
      }
    },
    loadingError (e) {
      // if you access to this page after 1-2 days, the image is expired
      // we try to reload the image 3 times before deleting it, so we make sure it's not a temporary issue (i.e. random internet issue)

      if (sessionStorage.getItem('error.' + this.photo.md5) >= 3) {
        this.$store.dispatch('onboarding/deletePhoto', { md5: this.photo.md5 })
        sessionStorage.removeItem('error.' + this.photo.md5)
        return
      }

      sessionStorage.setItem('error.' + this.photo.md5, (sessionStorage.getItem('error.' + this.photo.md5) || 0) + 1)
      this.random = Date.now()
    },
    onDragStart (event) {
      this.isDragging = true
      event.dataTransfer.setData('text/plain', JSON.stringify({
        md5: this.photo.md5,
        currentCategory: this.photo.fullBody ? 'fullBody' : 'face'
      }))
      event.dataTransfer.effectAllowed = 'move'
    },
    onDragEnd () {
      this.isDragging = false
    }
  }
}
</script>

<i18n>
  {
    "en": {
      "Are you sure you want to delete this photo?": "Are you sure you want to delete this photo?",
      "Delete": "Delete",
      "Mark as face photo": "Mark as face photo",
      "Mark as full body photo": "Mark as full body photo"
    },
    "es": {
      "Are you sure you want to delete this photo?": "¿Seguro que quieres eliminar esta foto?",
      "Delete": "Eliminar",
      "Mark as face photo": "Mover a fotos de cara",
      "Mark as full body photo": "Mover a fotos de cuerpo completo"
    },
    "de": {
      "Are you sure you want to delete this photo?": "Bist du sicher, dass du dieses Foto löschen möchtest?",
      "Delete": "Löschen",
      "Mark as face photo": "Zu Gesichtsfotos verschieben",
      "Mark as full body photo": "Zu Ganzkörperfotos verschieben"
    }
  }
</i18n>
