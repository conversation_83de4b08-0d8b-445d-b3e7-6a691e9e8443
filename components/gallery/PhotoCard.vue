<template>
  <div
    class="transition group relative box-content"
    :class="{ 'cursor-pointer': photoIsActive }"
    data-testid="photo-card"
    :data-photo-id="photo._id"
  >
    <ImageDns v-if="photoIsActive" :src="(watermark) ? (photo.thumbnail || photo.image) : photo.image" class="rounded-xl overflow-hidden w-full  transition duration-300 object-cover" />
    <template v-if="photoIsActive">
      <div v-if="icons?.includes('remixes') && photoIsRemix" class="absolute top-1 left-1 px-2 py-1 bg-teal-500 rounded-lg text-xs text-white group-hover:hidden">
        REMIX
      </div>
      <div v-if="icons?.includes('remixes') && photoIsEdit" class="absolute top-1 left-1 px-2 py-1 bg-teal-500 rounded-lg text-xs text-white group-hover:hidden">
        EDIT
      </div>
      <div v-if="icons?.includes('keepers') && photoIsKeeper" class="absolute top-0 right-0 p-2 bg-teal-500 rounded-xl rounded-tl-none rounded-br-none group-hover:hidden">
        <IconHeart class="w-4 h-4 text-white" />
      </div>
      <div v-if="icons?.includes('favorite') && photoIsFavorite" class="absolute top-0 right-0 p-2 bg-yellow-500 rounded-xl rounded-tl-none rounded-br-none group-hover:hidden">
        <IconStar class="w-4 h-4 text-white" />
      </div>
      <div v-if="photoHasVotes" class="absolute bottom-2 right-2 px-2 py-1 bg-yellow-500 rounded-lg text-xs text-white group-hover:hidden">
        {{ photo?.votes }} votes
      </div>
    </template>
    <ProfilePicture
      v-if="hasPfp"
      ref="profilePicture"
      class="absolute bottom-2 left-2"
      :pfp-url="photo.pfp"
      :branding="item?.branding || null"
      :photo-id="photo._id"
    />
    <div v-else-if="photoIsLoading" class="rounded-xl overflow-hidden relative w-full transition duration-300 bg-black flex items-center justify-center aspect-result">
      <img v-if="loadingPhoto" :src="loadingPhoto" class="absolute inset-0 w-full h-full object-cover blur-xl opacity-50 pointer-events-none" loading="lazy">
      <div class="p-3 flex items-center justify-center space-x-4 relative">
        <LoadingSpinnerWhite class="!w-5 !h-5" />
        <span class="text-white text-sm">Creating...</span>
      </div>
    </div>
    <template v-if="photoIsActive">
      <div class="group-hover:opacity-100 rounded-xl transition-opacity duration-300 absolute inset-0 opacity-0 flex items-end justify-center p-3">
        <div class="hidden sm:block absolute top-0 left-0 w-full h-full bg-black/60 rounded-xl" @click="handleAction('preview')" />
        <div class="block sm:hidden absolute top-0 left-0 w-full h-full bg-black/60 rounded-xl" />
        <div class="w-full justify-between relative hidden sm:flex">
          <button
            type="button"
            class="h-[38px] w-[41px] rounded-md bg-primary-500 flex items-center justify-center border border-white/20 shadow-[0px_1px_2px_rgba(0,0,0,0.05)]"
            :class="{ 'animate-pulse': isDownloading }"
            data-testid="photo-card-download-button-desktop"
            @click="handleAction('download')"
          >
            <IconCloudDownload class="w-4 h-4 text-white" />
          </button>
          <ButtonDropdown
            theme="v1"
            title="Actions"
            :items="actions"
            size="sm"
            icon="IconEllipsisVertical"
            icon-position="before"
            data-testid="photo-card-actions-dropdown-desktop"
            @select="handleAction"
          />
        </div>
        <div class="w-full h-full justify-center items-center relative flex flex-col gap-4 sm:hidden pointer-events-none group-hover:pointer-events-auto">
          <ButtonPrimary size="base" class="w-full" data-testid="photo-card-download-button-mobile" @click="handleAction('download')">
            <IconCloudDownload class="w-4 h-4 text-white mr-2 hidden sm:block" />
            <span class="hidden sm:block">Download photo</span>
            <span class="block sm:hidden">Download</span>
          </ButtonPrimary>
          <ButtonDropdown
            full-width
            theme="v1"
            title="All actions"
            :items="actions"
            size="base"
            icon="IconEllipsisVertical"
            icon-position="before"
            data-testid="photo-card-actions-dropdown-mobile"
            @select="handleAction"
          />
        </div>
      </div>
      <EditAdvanceModeModal
        v-if="modal === 'edit'"
        :watermark="watermark"
        :photo="photo"
        :model-id="$route.params.id"
        data-testid="photo-card-edit-modal"
        @closeModal="closeModal"
      />
    </template>
  </div>
</template>

<script>
import DownloadTrackingMixin from '../../mixins/DownloadTrackingMixin'
import ProfilePicture from './ProfilePicture.vue'

export default {
  components: {
    ProfilePicture
  },
  mixins: [DownloadTrackingMixin],
  props: {
    watermark: {
      type: Boolean,
      required: false,
      default: () => true
    },
    photo: {
      type: Object,
      required: true
    },
    icons: {
      type: Array,
      required: false,
      default: () => []
    }
  },
  data () {
    return {
      modal: null,
      isLoading: false,
      isDownloading: false
    }
  },
  computed: {
    hasPfp () {
      return this.photo?.pfp
    },
    photoHasVotes () {
      return this.photo?.votes > 0
    },
    item () {
      return this.$store.state.results.item
    },
    actions () {
      let actions = [
        { title: 'Preview', value: 'preview', icon: 'IconEye' },
        { title: 'Download', value: 'download', icon: 'IconCloudDownload' },
        { title: 'Mark as keeper', value: 'like', icon: 'IconHeart' },
        { title: 'Mark as dud', value: 'dislike', icon: 'IconSignalSlash' },
        { title: 'Edit', value: 'edit', icon: 'IconPencilSquare' }
      ]

      if (this.photo.likedStatus === 'favorite') {
        actions.splice(2, 1)
      } else if (this.photo.likedStatus === 'dud') {
        actions.splice(3, 1)
      }

      if (this.item?.organization) {
        if (this.photo.likedStatus !== 'loved') {
          actions.splice(2, 0, {
            title: 'Mark as favorite',
            value: 'loved',
            icon: 'IconStar'
          })
        }
      }

      actions = [
        ...actions,
        ...(this.hasPfp ? [{ title: 'Download PFP', value: 'download-pfp', icon: 'IconSolidUserCircle' }] : [])
      ]

      return actions
    },
    statePhoto () {
      return this.$store.state.results.item.images.find(image => image?._id?.toString() === this.photo._id?.toString())
    },
    isDownloaded () {
      return this.statePhoto?.isDownloaded || false
    },
    photoIsActive () {
      return this.statePhoto?.status === 'active'
    },
    photoIsLoading () {
      return ['waiting', 'pending', 'upscaling', 'editing', 'saving'].includes(this.statePhoto?.status)
    },
    photoIsRemix () {
      return this.statePhoto?.type === 'regeneration'
    },
    photoIsEdit () {
      return this.statePhoto?.type === 'edit'
    },
    photoIsKeeper () {
      return this.statePhoto?.likedStatus === 'favorite'
    },
    photoIsFavorite () {
      return this.statePhoto?.likedStatus === 'loved'
    },
    loadingPhoto () {
      if (!this.photoIsLoading) {
        return null
      }

      // first, we try to display the unscaled photo
      if (this.statePhoto.meta?.originalImage) {
        return this.statePhoto.meta.originalImage
      }

      // if there's no, we try to find the original photo
      if (this.statePhoto.meta?.originalPhoto) {
        const originalPhoto = this.$store.state.results.item.images.find(image => image?.identifier?.toString() === this.statePhoto.meta.originalPhoto.toString())
        if (originalPhoto) {
          return originalPhoto.thumbnail || originalPhoto.image
        }
      }

      // if there's no, we return the thumbnail if exists
      return this.statePhoto.thumbnail || this.statePhoto.image || null
    }
  },
  methods: {
    editPicture () {
      this.modal = 'edit'
    },
    previewPhoto () {
      this.$emit('preview')
    },
    closeModal () {
      this.modal = null
    },
    downloadPfp () {
      if (this.$refs.profilePicture) {
        this.$refs.profilePicture.downloadPfp()
      }
    },
    handleAction (value) {
      switch (value) {
        case 'preview':
          this.previewPhoto()
          break
        case 'like':
          this.markAsFavorite()
          break
        case 'dislike':
          this.markAsDud()
          break
        case 'loved':
          this.markAsLoved()
          break
        case 'edit':
          this.editPicture()
          break
        case 'download':
          this.downloadPhoto()
          break
        case 'download-pfp':
          this.downloadPfp()
          break
      }
    },
    downloadPhoto () {
      if (this.isDownloading) {
        return
      }

      this.isDownloading = true
      // Get the url from the photoId
      const url = this.photo?.upscaled || this.photo?.image
      this.$toast.success('Your image will download shortly. Make sure to accept the download prompt.')
      fetch(url)
        .then(response => response.blob())
        .then((blob) => {
          const url = window.URL.createObjectURL(new Blob([blob]))
          const link = document.createElement('a')
          link.href = url
          const fileType = blob.type.split('/')[1]
          const fileName = `${this.photo._id}-HeadshotPro.${fileType}`
          link.setAttribute('download', fileName)
          document.body.appendChild(link)
          link.click()
          document.body.removeChild(link)
          this.trackDownloads(this.photo.modelId, [this.photo._id])
          setTimeout(() => {
            this.$store.commit('results/SET_DOWNLOADED', [this.photo._id])
          }, 1000)
        })
        .finally(() => {
          this.isDownloading = false
        })
    },
    markAsFavorite () {
      this.$store.commit('results/SET_LIKED_STATUS', { id: this.photo._id, likedStatus: 'favorite' })
      this.$axios.$post('/model/favorite/add', { photoId: this.photo._id, modelId: this.photo.modelId })
        .then((response) => {
          if (response.success) {
            this.$toast.success('Photo added back to favorites.')
          }
        })
    },
    markAsLoved () {
      this.$store.commit('results/SET_LIKED_STATUS', { id: this.photo._id, likedStatus: 'loved' })
      this.$axios.$post('/model/favorite/loved', { photoId: this.photo._id, modelId: this.photo.modelId })
        .then((response) => {
          if (response.success) {
            this.$toast.success('Photo set as keeper.')
          }
        })
    },
    markAsDud () {
      this.$store.commit('results/SET_LIKED_STATUS', { id: this.photo._id, likedStatus: 'dud' })
      this.$axios.$post('/model/favorite/remove', { photoId: this.photo._id, modelId: this.photo.modelId })
        .then((response) => {
          if (response.success) {
            this.$toast.success('Photo removed from keepers.')
          }
        })
    }
  }
}
</script>
